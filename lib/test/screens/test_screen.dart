import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/cubits/dynamic_form_cubit.dart';
import 'package:serwis_app/core/models/serviceman.dart';
import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/core/models/visit/visit_form_type.dart';
import 'package:serwis_app/test/screens/dynamic_form_test_screen.dart';
import 'package:serwis_app/visit/cubit/visit_form_cubit.dart';
import 'package:serwis_app/visit/repository/visit_repository.dart';
import 'package:serwis_app/visit/screens/start_visit_form_screen.dart';

/// Ekran testowy służący do projektowania i testowania komponentów,
/// które docelowo będą dostępne tylko przy określonych warunkach w aplikacji.
/// Ten ekran jest dostępny tylko w trybie debug.
class TestScreen extends StatelessWidget {
  const TestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ekran testowy'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Ekran testowy',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            Text(
              'Ten ekran służy do projektowania i testowania komponentów, '
              'które docelowo będą dostępne tylko przy określonych warunkach w aplikacji.',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 32),
            const Divider(),
            const SizedBox(height: 16),
            Text(
              'Komponenty testowe:',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            // Lista komponentów testowych
            Expanded(
              child: ListView(
                children: [
                  ListTile(
                    title: const Text('Dynamiczny formularz'),
                    subtitle: const Text('Formularz generowany na podstawie schematu'),
                    leading: const Icon(Icons.dynamic_form),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const DynamicFormTestScreen(),
                        ),
                      );
                    },
                  ),
                  // ListTile(
                  //   title: const Text('StartVisitFormScreen'),
                  //   subtitle: const Text('Formularz generowany na podstawie schematu'),
                  //   leading: const Icon(Icons.dynamic_form),
                  //   trailing: const Icon(Icons.chevron_right),
                  //   onTap: () {
                  //     Navigator.of(context).push(
                  //       MaterialPageRoute(
                  //         builder: (context) => MultiBlocProvider(
                  //           providers: [
                  //             BlocProvider(
                  //               create: (context) => VisitFormCubit(

                  //                 visit: Visit(
                  //                   id: 13071,
                  //                   incidentId: 123,
                  //                   visitNumber: 456,
                  //                   status: VisitModelStatus.attended,
                  //                   serviceman: Serviceman(
                  //                     id: 0,
                  //                     jrUsername: 'test',
                  //                   ),
                  //                   location: null,
                  //                   tasks: [],
                  //                   visitPhotos: [],
                  //                   plannedDate: null,
                  //                   device: null,
                  //                   isSurveyRelated: false,
                  //                   isInstallationOnly: false,
                  //                   visitAttendedTime: null,
                  //                 ),
                  //                 visitRepository: context.read<VisitRepository>(),
                  //               )..getVisitForm(VisitFormType.visitStart),
                  //             ),
                  //             BlocProvider(
                  //               create: (context) => DynamicFormCubit(),
                  //             ),
                  //           ],
                  //           child: const StartVisitFormScreen(),
                  //         ),
                  //       ),
                  //     );
                  //   },
                  // ),
                  // Tutaj można dodawać więcej komponentów testowych
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
