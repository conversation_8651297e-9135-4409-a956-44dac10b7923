import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/cubits/dynamic_form_cubit.dart';
import 'package:serwis_app/core/models/form_field_schema.dart';
import 'package:serwis_app/core/widgets/dynamic_form_view.dart';

/// Ekran testowy do demonstracji działania dynamicznego formularza
class DynamicFormTestScreen extends StatelessWidget {
  const DynamicFormTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => DynamicFormCubit()..initForm(_getExampleSchema()),
      child: const _DynamicFormTestView(),
    );
  }

  /// Zwraca przykładowy schemat formularza
  List<FormFieldSchema> _getExampleSchema() {
    return [
      const FormFieldSchema(
        name: 'chemical',
        label: 'Chemia',
        type: 'text',
        required: true,
        value: 'chemia',
      ),
      const FormFieldSchema(
        name: 'workHours',
        label: 'Godziny pracy',
        type: 'number',
        required: true,
        value: '6',
      ),
      const FormFieldSchema(
        name: 'distance',
        label: 'Dystans (km)',
        type: 'number',
        required: false,
        value: '8',
      ),
      const FormFieldSchema(
        name: 'completed',
        label: 'Zakończone',
        type: 'checkbox',
        required: false,
        value: false,
      ),
      const FormFieldSchema(
        name: 'priority',
        label: 'Priorytet',
        type: 'select',
        required: true,
        value: ['Niski', 'Średni', 'Wysoki'],
      ),
    ];
  }
}

class _DynamicFormTestView extends StatelessWidget {
  const _DynamicFormTestView();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test formularza dynamicznego'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: BlocBuilder<DynamicFormCubit, DynamicFormState>(
          builder: (context, state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Formularz dynamiczny',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                DynamicFormView(
                  schema: state.schema,
                  formKey: state.formKey,
                  onFieldChanged: (name, value) {
                    context.read<DynamicFormCubit>().updateField(name, value);
                  },
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        context.read<DynamicFormCubit>().resetForm();
                      },
                      child: const Text('Resetuj'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        if (context.read<DynamicFormCubit>().validateForm()) {
                          final values = context.read<DynamicFormCubit>().getFormValues();
                          _showFormValues(context, values);
                        }
                      },
                      child: const Text('Zatwierdź'),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                const Text(
                  'Aktualne wartości:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                _buildCurrentValues(state.values),
              ],
            );
          },
        ),
      ),
    );
  }

  /// Buduje widok aktualnych wartości formularza
  Widget _buildCurrentValues(Map<String, dynamic> values) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: values.entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Row(
                children: [
                  Text(
                    '${entry.key}: ',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Expanded(
                    child: Text(
                      entry.value?.toString() ?? 'null',
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Wyświetla dialog z wartościami formularza
  void _showFormValues(BuildContext context, Map<String, dynamic> values) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Wartości formularza'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: values.entries.map((entry) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Row(
                    children: [
                      Text(
                        '${entry.key}: ',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Expanded(
                        child: Text(
                          entry.value?.toString() ?? 'null',
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }
}
