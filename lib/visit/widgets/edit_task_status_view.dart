import 'package:flutter/material.dart';

import 'package:serwis_app/core/models/task.dart';
import 'package:serwis_app/visit/widgets/task_status_chip.dart';

class EditTaskStatusView extends StatefulWidget {
  const EditTaskStatusView({
    super.key,
    required this.task,
  });

  final Task task;

  @override
  State<EditTaskStatusView> createState() => _EditTaskStatusViewState();
}

class _EditTaskStatusViewState extends State<EditTaskStatusView> {
  late TaskStatus _selectedStatus;

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.task.status;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Nagłówek
          Center(
            child: Text(
              'Zmień status zadania',
              style: textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Opis zadania
          Text(
            'Opis:',
            style: textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            widget.task.description ?? 'Brak opisu zadania',
            style: textTheme.bodyMedium,
          ),

          const SizedBox(height: 16),

          // Symptom zadania
          Text(
            'Symptom:',
            style: textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            widget.task.symptom ?? 'Brak symptomu',
            style: textTheme.bodyMedium,
          ),

          const SizedBox(height: 24),

          // Wybór statusu
          Text(
            'Wybierz status:',
            style: textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          // Statusy do wyboru
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 8,
            children: [
              // Zaplanowane
              TaskStatusChip(
                status: TaskStatus.planned,
                isSelected: _selectedStatus == TaskStatus.planned,
                onTap: () {
                  setState(() {
                    _selectedStatus = TaskStatus.planned;
                  });
                },
              ),

              // Zakończone
              TaskStatusChip(
                status: TaskStatus.completed,
                isSelected: _selectedStatus == TaskStatus.completed,
                onTap: () {
                  setState(() {
                    _selectedStatus = TaskStatus.completed;
                  });
                },
              ),

              // Nie dotyczy
              TaskStatusChip(
                status: TaskStatus.notApplicable,
                isSelected: _selectedStatus == TaskStatus.notApplicable,
                onTap: () {
                  setState(() {
                    _selectedStatus = TaskStatus.notApplicable;
                  });
                },
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Przycisk zatwierdzenia
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                // Tutaj będzie implementacja zmiany statusu
                Navigator.of(context).pop(_selectedStatus);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Zatwierdź'),
            ),
          ),
        ],
      ),
    );
  }
}
