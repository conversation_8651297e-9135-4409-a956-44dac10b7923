import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';

import 'package:geolocator/geolocator.dart'; // Add this import for geolocation
import 'package:image_picker/image_picker.dart';

import 'package:serwis_app/core/constants/system_settings.dart'; // Add this import
import 'package:serwis_app/visit/repository/visit_repository.dart';
import 'package:serwis_app/core/models/device.dart'; // Add this line to import the Device class
import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/core/models/visit/visit_photo.dart';
import 'package:serwis_app/core/models/visit/visit_photo_list.dart'; // Import VisitPhotosData
import 'package:serwis_app/visit/models/visit_photo_category.dart'; // Import VisitPhotoCategory

class VisitPhotosList extends StatefulWidget {
  const VisitPhotosList({
    super.key,
    // required this.visitRepository,
    required this.photos,
    required this.device,
    required this.visit,
    this.photoCategories,
  });

  // final VisitRepository visitRepository;
  final List<VisitPhoto> photos;
  final Device device;
  final Visit visit;
  final List<VisitPhotoCategory>? photoCategories;

  @override
  State<VisitPhotosList> createState() => _VisitPhotosListState();
}

class _VisitPhotosListState extends State<VisitPhotosList> {
  final List<int> _prtsPairs = [];
  final Map<String, bool> _highlightedButtons = {};
  final bool _emulatePhotoAddition = false; // Add this flag to switch emulation on/off
  bool _isSubmitting = false; // Add this flag to track submission state
  String? _errorMessage; // Add this to store error messages
  bool _hasUnsavedChanges = false; // Add this flag to track unsaved changes
  List<VisitPhotoCategory>? _photoCategories;

  @override
  void initState() {
    super.initState();
    _initializePrtsPairs();
    _initializePhotoCategories();
  }

  void _initializePhotoCategories() {
    // Jeśli kategorie nie zostały przekazane, tworzymy domyślne kategorie z mapy
    if (widget.photoCategories == null) {
      // Przykładowa mapa do utworzenia kategorii zdjęć
      final Map<String, dynamic> categoryMap = {
        'code': 'WRSD',
        'name': 'Najgorsze stanowisko',
        'isRequired': _isWrsdMandatory(),
        'allowMultiple': false,
        'subCategories': null,
      };

      // Tworzenie obiektu VisitPhotoCategory z mapy
      final wrsdCategory = VisitPhotoCategory.fromMap(categoryMap);

      // Tworzenie kategorii GFLT z podkategoriami
      final Map<String, dynamic> gfltMap = {
        'code': 'GFLT',
        'name': 'Filtry (ogólne)',
        'isRequired': false,
        'allowMultiple': true,
        'subCategories': [
          {
            'code': 'FLT',
            'name': 'Filtr wejściowy',
            'isRequired': _isGfltMandatory(),
            'allowMultiple': false,
          },
          {
            'code': 'O1FL',
            'name': 'Filtr osmozy I stopień',
            'isRequired': _isGfltMandatory(),
            'allowMultiple': false,
          },
          {
            'code': 'O2FL',
            'name': 'Filtr osmozy II stopień',
            'isRequired': _isGfltMandatory(),
            'allowMultiple': false,
          }
        ],
      };

      final gfltCategory = VisitPhotoCategory.fromMap(gfltMap);

      // Tworzenie kategorii WRKP
      final Map<String, dynamic> wrkpMap = {
        'code': 'WRKP',
        'name': 'Przygotowane stanowisko pracy',
        'isRequired': true,
        'allowMultiple': false,
      };

      final wrkpCategory = VisitPhotoCategory.fromMap(wrkpMap);

      // Tworzenie kategorii PRTS
      final Map<String, dynamic> prtsMap = {
        'code': 'PRTS',
        'name': 'Wymiana części',
        'isRequired': false,
        'allowMultiple': true,
      };

      final prtsCategory = VisitPhotoCategory.fromMap(prtsMap);

      // Tworzenie kategorii CCNT z podkategoriami
      final Map<String, dynamic> ccntMap = {
        'code': 'CCNT',
        'name': 'Stan licznika obrotu',
        'isRequired': false,
        'allowMultiple': false,
        'subCategories': [
          {
            'code': 'BFR',
            'name': 'Przed',
            'isRequired': false,
            'allowMultiple': false,
          },
          {
            'code': 'AFT',
            'name': 'Po',
            'isRequired': false,
            'allowMultiple': false,
          }
        ],
      };

      final ccntCategory = VisitPhotoCategory.fromMap(ccntMap);

      // Dodanie kategorii CFLT jeśli jest widoczna
      List<VisitPhotoCategory> categories = [wrsdCategory, gfltCategory, wrkpCategory, prtsCategory, ccntCategory];

      if (_isCfltVisible()) {
        final Map<String, dynamic> cfltMap = {
          'code': 'CFLT',
          'name': 'Filtry cyrkulacji',
          'isRequired': _isCfltMandatory(),
          'allowMultiple': false,
        };

        final cfltCategory = VisitPhotoCategory.fromMap(cfltMap);
        categories.insert(2, cfltCategory); // Wstawiamy po GFLT, przed WRKP
      }

      _photoCategories = categories;
    } else {
      _photoCategories = widget.photoCategories;
    }
  }

  void _initializePrtsPairs() {
    final prtsPhotosCount = widget.photos.where((photo) => photo.category == 'PRTS').length;
    final pairsCount = (prtsPhotosCount / 2).ceil();
    for (int i = 0; i < pairsCount; i++) {
      _prtsPairs.add(i);
    }
// print('Initialized PRTS pairs: $_prtsPairs'); // Commented out print statement
  }

  Future<void> _takePicture(BuildContext context, String category, String subCategory) async {
    if (_emulatePhotoAddition) {
      // Emulate adding a photo for testing purposes
      final newPhoto = VisitPhoto(
        id: 0,
        photoFileName: 'test_photo.jpg',
        category: category,
        subCategory: subCategory,
        fileContent: 'base64EncodedString',
      );

      if (mounted) {
        setState(() {
          widget.photos.add(newPhoto);
          _hasUnsavedChanges = true; // Set the flag when a photo is added
        });
      }
    } else {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.camera);

      if (pickedFile != null) {
        // Read file bytes and convert to base64
        final file = File(pickedFile.path);
        final bytes = await file.readAsBytes();
        final base64Image = base64Encode(bytes);

        // Create a VisitPhoto with the base64 content included
        final newPhoto = VisitPhoto(
          id: 0,
          photoFileName: pickedFile.path, // you can optionally adjust this field if needed
          category: category,
          subCategory: subCategory,
          fileContent: base64Image,
        );

        if (mounted) {
          setState(() {
            widget.photos.add(newPhoto);
            _hasUnsavedChanges = true; // Set the flag when a photo is added
          });
        }
      }
    }
  }

  void _deletePhoto(VisitPhoto photo) {
    setState(() {
      widget.photos.remove(photo);
      _hasUnsavedChanges = true; // Set the flag when a photo is deleted
    });
  }

  void _addPrtsPair() {
    setState(() {
      _prtsPairs.add(_prtsPairs.length);
    });
  }

  void _removePrtsPair(int index) {
    setState(() {
      _prtsPairs.removeAt(index);
      widget.photos.removeWhere(
          (photo) => photo.subCategory == 'Part${index + 1}-BFR' || photo.subCategory == 'Part${index + 1}-AFT');
    });
  }

  bool _isCfltVisible() {
    final month = widget.visit.plannedDate?.month ?? 0;
    return (month >= 9 || month <= 3);
  }

  bool _isCfltMandatory() {
    return _isCfltVisible() && widget.visit.isSurveyRelated && !widget.visit.isInstallationOnly;
  }

  bool _isWrsdMandatory() {
    return widget.visit.isSurveyRelated && !widget.visit.isInstallationOnly;
  }

  bool _isGfltMandatory() {
    return widget.visit.isSurveyRelated && !widget.visit.isInstallationOnly;
  }

  bool _checkMandatoryPhotos() {
    final Map<String, int> categoryCounts = {
      'WRSD': 0,
      'GFLT': 0,
      'CFLT': 0,
      'WRKP': 0,
      'PRTS': 0,
      'CCNT': 0,
      'ROPE': 0,
    };

    final Map<String, int> subCategoryCounts = {};

    for (var photo in widget.photos) {
      if (categoryCounts.containsKey(photo.category)) {
        categoryCounts[photo.category] = categoryCounts[photo.category]! + 1;
      }
      if (photo.category == 'ROPE' || photo.category == 'PRTS' || photo.category == 'CCNT') {
        if (photo.subCategory != null) {
          subCategoryCounts[photo.subCategory!] = (subCategoryCounts[photo.subCategory!] ?? 0) + 1;
        }
      }
    }

    bool allMandatoryPhotosPresent = true;

    // if (SystemSettings.extraRopePhotosClientIds.contains(widget.device.owner.id)) {
    //   for (int i = 1; i <= (widget.device.numberOfStands ?? 0); i++) {
    //     if ((subCategoryCounts['stand$i'] ?? 0) < 1) {
    //       allMandatoryPhotosPresent = false;
    //       _highlightButton('ROPE', 'stand$i');
    //     }
    //   }
    // }

    if (widget.visit.isSurveyRelated) {
      if (_isWrsdMandatory() && categoryCounts['WRSD']! < 1) {
        allMandatoryPhotosPresent = false;
        _highlightButton('WRSD', '');
      }
      if (_isGfltMandatory() && categoryCounts['GFLT']! < 3) {
        allMandatoryPhotosPresent = false;
        _highlightButton('GFLT', 'FLT');
        _highlightButton('GFLT', 'O1FL');
        _highlightButton('GFLT', 'O2FL');
      }
      if (_isCfltMandatory() && categoryCounts['CFLT']! < 1) {
        allMandatoryPhotosPresent = false;
        _highlightButton('CFLT', '');
      }
    }

    if (categoryCounts['WRKP']! < 1) {
      allMandatoryPhotosPresent = false;
      _highlightButton('WRKP', '');
    }

    for (int i = 0; i < _prtsPairs.length; i++) {
      if ((subCategoryCounts['Part${i + 1}-BFR'] ?? 0) < 1) {
        allMandatoryPhotosPresent = false;
        _highlightButton('PRTS', 'Part${i + 1}-BFR');
      }
      if ((subCategoryCounts['Part${i + 1}-AFT'] ?? 0) < 1) {
        allMandatoryPhotosPresent = false;
        _highlightButton('PRTS', 'Part${i + 1}-AFT');
      }
    }

    // Check CCNT subcategories
    if ((subCategoryCounts['BFR'] ?? 0) > 0 && (subCategoryCounts['AFT'] ?? 0) < 1) {
      allMandatoryPhotosPresent = false;
      _highlightButton('CCNT', 'AFT');
    }
    if ((subCategoryCounts['AFT'] ?? 0) > 0 && (subCategoryCounts['BFR'] ?? 0) < 1) {
      allMandatoryPhotosPresent = false;
      _highlightButton('CCNT', 'BFR');
    }

    return allMandatoryPhotosPresent;
  }

  void _highlightButton(String category, String subCategory) {
    final key = '$category-$subCategory';
    if (mounted) {
      setState(() {
        _highlightedButtons[key] = true;
      });
    }
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _highlightedButtons[key] = false;
        });
      }
    });
  }

  // Add this method to calculate the distance between two coordinates
  double _calculateDistance(double startLatitude, double startLongitude, double endLatitude, double endLongitude) {
    return Geolocator.distanceBetween(startLatitude, startLongitude, endLatitude, endLongitude);
  }

  // Add this method to get the current location of the device
  Future<Position> _getCurrentLocation() async {
    return await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
  }

  // Modify the _submit method to include the distance check
  void _submit() async {
    final visitLocation = widget.visit.location;

    if (visitLocation != null) {
      try {
        final currentPosition = await _getCurrentLocation();
        final distance = _calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          visitLocation.lat,
          visitLocation.lon,
        );

        if (distance > SystemSettings.nearbyDistanceThreshold) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Zdjęcia należy wysłać z lokalizcji obiektu!', style: TextStyle(color: Colors.white)),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      } catch (e) {
        setState(() {
          _errorMessage = 'Nie można uzyskać bieżącej lokalizacji: $e';
        });
        return;
      }
    }

    if (_checkMandatoryPhotos()) {
      setState(() {
        _isSubmitting = true; // Set the flag to true when submission starts
        _errorMessage = null; // Clear any previous error messages
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Wysyłanie zdjęc - proszę czekać')),
      );
      try {
        // Create a new VisitPhotosData object for each submission
        final visitPhotosData = VisitPhotoList(data: List.from(widget.photos), count: widget.photos.length);
        // await widget.visitRepository
        //     .addVisitPhotos(widget.visit.incidentId, visitPhotosData)
        //     .timeout(const Duration(seconds: 120)); // Extend timeout to 120 seconds
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Wszystkie wymagane zdjęcia zostały dodane.')),
        );
        Navigator.pop(context, true); // Pass true to indicate success
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Błąd podczas wysyłania zdjęć: $e')),
        );
        setState(() {
          _isSubmitting = false; // Reset the flag if submission fails
        });
      } finally {
        if (mounted) {
          setState(() {
            _isSubmitting = false; // Reset the flag when submission ends
          });
        }
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Proszę dodać wszystkie wymagane zdjęcia.', style: TextStyle(color: Colors.white)),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<bool> _onWillPop() async {
    if (_hasUnsavedChanges) {
      return await showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Masz niezapisane zmiany'),
              content: const Text('Czy na pewno chcesz kontynuować?'),
              actions: <Widget>[
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Nie'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('Tak'),
                ),
              ],
            ),
          ) ??
          false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    // Konwersja listy kategorii na mapę dla zachowania kompatybilności z istniejącym kodem
    final Map<String, String> categories = {};
    final Map<String, Map<String, String>> subCategories = {};

    // Wypełnianie map na podstawie _photoCategories
    if (_photoCategories != null) {
      for (var category in _photoCategories!) {
        // Dodanie głównej kategorii
        categories[category.code] = '${category.name} ${category.isRequired ? "*" : ""}';

        // Dodanie podkategorii jeśli istnieją
        if (category.subCategories != null && category.subCategories!.isNotEmpty) {
          Map<String, String> subCategoryMap = {};

          for (var subCategory in category.subCategories!) {
            subCategoryMap[subCategory.code] = '${subCategory.name} ${subCategory.isRequired ? "*" : ""}';
          }

          subCategories[category.code] = subCategoryMap;
        }
      }
    } else {
      // Zachowanie oryginalnej logiki jako fallback
      categories.addAll({
        'WRSD': 'Najgorsze stanowisko ${_isWrsdMandatory() ? "*" : ""}',
        'GFLT': 'Filtry (ogólne)',
        if (_isCfltVisible()) 'CFLT': 'Filtry cyrkulacji ${_isCfltMandatory() ? "*" : ""}',
        'WRKP': 'Przygotowane stanowisko pracy *',
        'PRTS': 'Wymiana części',
        'CCNT': 'Stan licznika obrotu',
      });

      subCategories.addAll({
        'GFLT': {
          'FLT': 'Filtr wejściowy ${_isGfltMandatory() ? "*" : ""}',
          'O1FL': 'Filtr osmozy I stopień ${_isGfltMandatory() ? "*" : ""}',
          'O2FL': 'Filtr osmozy II stopień ${_isGfltMandatory() ? "*" : ""}',
        },
        'PRTS': {
          for (int i = 0; i < _prtsPairs.length; i++) 'Part${i + 1}-BFR': 'Part${i + 1} Przed *',
          for (int i = 0; i < _prtsPairs.length; i++) 'Part${i + 1}-AFT': 'Part${i + 1} Po *',
        },
        'CCNT': {
          'BFR': 'Przed',
          'AFT': 'Po',
        },
      });
    }

    // Dodanie specjalnych podkategorii dla PRTS, które są dynamiczne
    if (categories.containsKey('PRTS')) {
      subCategories['PRTS'] = {
        for (int i = 0; i < _prtsPairs.length; i++) 'Part${i + 1}-BFR': 'Part${i + 1} Przed *',
        for (int i = 0; i < _prtsPairs.length; i++) 'Part${i + 1}-AFT': 'Part${i + 1} Po *',
      };
    }

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: Text(
              'Zdjęcia z wizyty serwisowej',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
          ),
          if (widget.photos.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Text(
                'Zdjęcia wizyty: ${widget.photos.length}',
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
          Column(
            children: categories.entries.map((entry) {
              final categoryPhotos = widget.photos.where((photo) => photo.category == entry.key).toList();
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Divider(),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Text(
                      entry.value,
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ),
                  if (entry.key == 'PRTS')
                    Column(
                      children: [
                        ElevatedButton(
                          onPressed: _addPrtsPair,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue, // Change the background color
                            padding:
                                const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0), // Reduce padding to half
                            textStyle: const TextStyle(fontSize: 16.0), // Increase font size
                          ),
                          child: const Text('Dodaj nową część'),
                        ),
                        const SizedBox(height: 16.0), // Add more space before the buttons
                        ..._prtsPairs.asMap().entries.map((pair) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 4.0),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Część #${pair.value + 1} *',
                                      style: Theme.of(context).textTheme.bodyMedium,
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.delete, color: Colors.orange),
                                      onPressed: () => _removePrtsPair(pair.key),
                                    ),
                                  ],
                                ),
                              ),
                              ElevatedButton(
                                onPressed: () => _takePicture(context, entry.key, 'Part${pair.value + 1}-BFR'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: _highlightedButtons['${entry.key}-Part${pair.value + 1}-BFR'] == true
                                      ? Colors.red
                                      : null,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0, vertical: 6.0), // Reduce padding to half
                                ),
                                child: const Text('Dodaj zdjęcie Przed'),
                              ),
                              if (categoryPhotos.any((photo) => photo.subCategory == 'Part${pair.value + 1}-BFR'))
                                Column(
                                  children: categoryPhotos
                                      .where((photo) => photo.subCategory == 'Part${pair.value + 1}-BFR')
                                      .map((photo) {
                                    return Card(
                                      child: ListTile(
                                        title: Text(
                                          photo.photoFileName,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        trailing: IconButton(
                                          icon: const Icon(
                                            Icons.delete,
                                            color: Colors.red,
                                          ),
                                          onPressed: () => _deletePhoto(photo),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              const SizedBox(height: 10.0), // Add more space between the buttons
                              const Divider(
                                  color: Colors.grey, height: 1.0), // Ensure the gray separation line is displayed
                              ElevatedButton(
                                onPressed: () => _takePicture(context, entry.key, 'Part${pair.value + 1}-AFT'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: _highlightedButtons['${entry.key}-Part${pair.value + 1}-AFT'] == true
                                      ? Colors.red
                                      : null,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0, vertical: 6.0), // Reduce padding to half
                                ),
                                child: const Text('Dodaj zdjęcie Po'),
                              ),
                              if (categoryPhotos.any((photo) => photo.subCategory == 'Part${pair.value + 1}-AFT'))
                                Column(
                                  children: categoryPhotos
                                      .where((photo) => photo.subCategory == 'Part${pair.value + 1}-AFT')
                                      .map((photo) {
                                    return Card(
                                      child: ListTile(
                                        title: Text(
                                          photo.photoFileName,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        trailing: IconButton(
                                          icon: const Icon(
                                            Icons.delete,
                                            color: Colors.red,
                                          ),
                                          onPressed: () => _deletePhoto(photo),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              if (pair.key !=
                                  _prtsPairs.length -
                                      1) // Add this condition to avoid adding the divider after the last subsection
                                const Divider(
                                    color: Colors.blue,
                                    height: 1.0), // Add separation line with the same color as the button
                            ],
                          );
                        }),
                      ],
                    )
                  else if (subCategories.containsKey(entry.key))
                    ...subCategories[entry.key]!.entries.map((subEntry) {
                      final subCategoryPhotos =
                          categoryPhotos.where((photo) => photo.subCategory == subEntry.key).toList();
                      final key = '${entry.key}-${subEntry.key}';
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4.0),
                            child: Text(
                              subEntry.value,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                          ElevatedButton(
                            onPressed: () => _takePicture(context, entry.key, subEntry.key),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _highlightedButtons[key] == true ? Colors.red : null,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0), // Reduce padding to half
                            ),
                            child: const Text('Dodaj zdjęcie'),
                          ),
                          if (subCategoryPhotos.isNotEmpty)
                            Column(
                              children: subCategoryPhotos.map((photo) {
                                return Card(
                                  child: ListTile(
                                    title: Text(
                                      photo.photoFileName,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    trailing: IconButton(
                                      icon: const Icon(Icons.delete, color: Colors.red),
                                      onPressed: () => _deletePhoto(photo),
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          if (subEntry.key == 'BFR' || subEntry.key == 'AFT' || entry.key == 'ROPE')
                            if (subEntry.key != subCategories[entry.key]!.keys.last)
                              const Divider(
                                  color: Colors.grey,
                                  height: 1.0), // Add grrey separation line except for the last sub-section
                        ],
                      );
                    })
                  else
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ElevatedButton(
                          onPressed: () => _takePicture(context, entry.key, ''),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _highlightedButtons['${entry.key}-'] == true ? Colors.red : null,
                            padding:
                                const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0), // Reduce padding to half
                          ),
                          child: const Text('Dodaj zdjęcie'),
                        ),
                        if (categoryPhotos.isNotEmpty)
                          Column(
                            children: categoryPhotos.map((photo) {
                              return Card(
                                child: ListTile(
                                  title: Text(
                                    photo.photoFileName,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  trailing: IconButton(
                                    icon: const Icon(Icons.delete, color: Colors.red),
                                    onPressed: () => _deletePhoto(photo),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                      ],
                    ),
                ],
              );
            }).toList(),
          ),
          // Add submit button
          if (_errorMessage != null)
            Container(
              color: Colors.red,
              padding: const EdgeInsets.all(8.0),
              child: Text(
                _errorMessage!,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Column(
              children: [
                const Divider(
                  color: Colors.green, // Set divider color to green
                  thickness: 2.0, // Set divider thickness to 2px
                ),
                ElevatedButton(
                  onPressed: _isSubmitting ? null : _submit, // Disable button if submitting
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green, // Set background color to green
                    padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0), // Reduce padding to half
                  ),
                  child: _isSubmitting
                      ? const CircularProgressIndicator()
                      : const Text('Wyślij'), // Show loading indicator if submitting
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
