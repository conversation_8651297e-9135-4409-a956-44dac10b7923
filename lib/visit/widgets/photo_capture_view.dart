import 'dart:io';

import 'package:flutter/material.dart';

import 'package:serwis_app/core/services/photo_service.dart';

class PhotoCaptureView extends StatefulWidget {
  final String description;
  final bool canSkip;
  final Function(File) onPhotoTaken;
  final VoidCallback onSkip;

  const PhotoCaptureView({
    super.key,
    required this.description,
    required this.canSkip,
    required this.onPhotoTaken,
    required this.onSkip,
  });

  @override
  State<PhotoCaptureView> createState() => _PhotoCaptureViewState();
}

class _PhotoCaptureViewState extends State<PhotoCaptureView> {
  File? _imageFile;
  bool _isCapturing = false;

  Future<void> _takePhoto() async {
    setState(() {
      _isCapturing = true;
    });

    try {
      final photoService = PhotoService.instance;
      final imageFile = await photoService.takePhoto();

      if (imageFile != null) {
        setState(() {
          _imageFile = imageFile;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Błąd podczas operacji na zdjęciu: $e')),
        );
      }
    } finally {
      setState(() {
        _isCapturing = false;
      });
    }
  }

  void _confirmPhoto() {
    if (_imageFile != null) {
      widget.onPhotoTaken(_imageFile!);
    }
  }

  void _retakePhoto() {
    setState(() {
      _imageFile = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            widget.description,
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          if (_imageFile == null) ...[
            Column(
              children: [
                _buildCameraButton(),
                if (widget.canSkip) ...[
                  const SizedBox(height: 8),
                  _buildSkipButton(),
                ],
              ],
            ),
          ] else ...[
            _buildImagePreview(),
            const SizedBox(height: 8),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }

  Widget _buildCameraButton() {
    return ElevatedButton.icon(
      onPressed: _isCapturing ? null : _takePhoto,
      icon: const Icon(Icons.camera_alt),
      label: _isCapturing ? const Text('Robienie zdjęcia...') : const Text('Zrób zdjęcie'),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    );
  }

  Widget _buildSkipButton() {
    return TextButton(
      onPressed: widget.onSkip,
      child: const Text('Pomiń'),
    );
  }

  Widget _buildImagePreview() {
    return Container(
      height: 300,
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.file(
          _imageFile!,
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      spacing: 16,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TextButton.icon(
          onPressed: _retakePhoto,
          icon: const Icon(Icons.refresh),
          label: const Text('Zrób ponownie'),
        ),
        ElevatedButton.icon(
          onPressed: _confirmPhoto,
          icon: const Icon(Icons.check),
          label: const Text('Zatwierdź'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }
}
