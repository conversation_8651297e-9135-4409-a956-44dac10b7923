import 'package:flutter/material.dart';

import 'package:serwis_app/core/models/visit/visit.dart';

class VisitDetailsCard extends StatefulWidget {
  const VisitDetailsCard({
    super.key,
    required this.visit,
  });

  final Visit visit;

  @override
  State<VisitDetailsCard> createState() => _VisitDetailsCardState();
}

class _VisitDetailsCardState extends State<VisitDetailsCard> with SingleTickerProviderStateMixin {
  late bool _isExpanded = widget.visit.status == VisitModelStatus.planned;
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
      value: _isExpanded ? 1.0 : 0.0, // Ustawiamy początkową wartość na podstawie widget.isExpanded
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    const space = 12.0;
    // final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      // color: colorScheme.surfaceContainerHigh,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Nagłówek z możliwością zwijania/rozwijania
          ListTile(
            shape: RoundedRectangleBorder(
              borderRadius: _isExpanded ? BorderRadius.vertical(top: Radius.circular(12)) : BorderRadius.circular(12),
            ),
            onTap: _toggleExpanded,
            title: Text(
              'Szczegóły wizyty',
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            trailing: RotationTransition(
              turns: Tween(begin: 0.0, end: 0.5).animate(_controller),
              child: const Icon(Icons.expand_less),
            ),
            dense: true,
            visualDensity: VisualDensity.compact,
            contentPadding: const EdgeInsets.symmetric(horizontal: space),
          ),

          // Zawartość karty z animacją
          SizeTransition(
            sizeFactor: _animation,
            axisAlignment: -1.0, // Animacja od góry do dołu
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Informacje o urządzeniu
                Padding(
                  padding: const EdgeInsets.all(space),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Informacje o urządzeniu',
                          style: textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 6),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Nr seryjny', style: textTheme.labelSmall),
                                Text(widget.visit.device?.serialNumber.toString() ?? '-', style: textTheme.bodySmall),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const Divider(height: 0),

                Padding(
                  padding: const EdgeInsets.all(space),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Lokalizacja
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Lokalizacja',
                              style: textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 6),
                            Text(
                              '${widget.visit.location?.name}',
                              style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              '${widget.visit.location?.address}, ${widget.visit.location?.city}, ${widget.visit.location?.country}',
                              style: textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ),

                      // Typ wizyty (Przegląd/Montaż)
                      if (widget.visit.isSurveyRelated || widget.visit.isInstallationOnly)
                        Padding(
                          padding: const EdgeInsets.only(left: 8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (widget.visit.isSurveyRelated)
                                Chip(
                                  label: Text('Przegląd', style: TextStyle(fontSize: 11)),
                                  visualDensity: VisualDensity.compact,
                                ),
                              if (widget.visit.isInstallationOnly)
                                Padding(
                                  padding: EdgeInsets.only(top: widget.visit.isSurveyRelated ? 2 : 0),
                                  child: Chip(
                                    label: Text('Montaż', style: TextStyle(fontSize: 11)),
                                    visualDensity: VisualDensity.compact,
                                  ),
                                ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
