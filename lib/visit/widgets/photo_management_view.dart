import 'package:flutter/material.dart';

class PhotoManagementView extends StatelessWidget {
  const PhotoManagementView({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.photo_library,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'Zarządzanie zdjęciami',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          const Text(
            'Ta funkcjonalność zostanie zaimplementowana wkrótce.',
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
