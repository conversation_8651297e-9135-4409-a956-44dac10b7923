// Enum do okreś<PERSON>ia, czy jeste<PERSON><PERSON> w trakcie robienia zdjęcia, czy zarządzania nimi
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/visit/repository/visit_repository.dart';

enum VisitPhotosMode {
  takingPhotos,
  managingPhotos,
}

enum VisitPhotosStatus {
  loading,
  ready,
  error,
}

class VisitPhotosState {
  final VisitPhotosMode mode;
  final VisitPhotosStatus status;

  const VisitPhotosState({
    required this.mode,
    required this.status,
  });

  VisitPhotosState copyWith({
    VisitPhotosMode? mode,
    VisitPhotosStatus? status,
  }) {
    return VisitPhotosState(
      mode: mode ?? this.mode,
      status: status ?? this.status,
    );
  }
}

class VisitPhotosCubit extends Cubit<VisitPhotosState> {
  final VisitRepository visitRepository;

  VisitPhotosCubit({
    required this.visitRepository,
    required int visitId,
    required VisitPhotosMode mode,
  }) : super(
          VisitPhotosState(
            mode: mode,
            status: VisitPhotosStatus.loading,
          ),
        );

  void switchMode(VisitPhotosMode mode) {
    emit(state.copyWith(mode: mode));
  }

  void loadPhotos() {
    emit(state.copyWith(status: VisitPhotosStatus.loading));
    // TODO: Load photos
    emit(state.copyWith(status: VisitPhotosStatus.ready));
  }
}
