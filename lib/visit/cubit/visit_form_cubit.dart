import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/models/form_field_schema.dart';
import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/core/models/visit/visit_form_type.dart';
import 'package:serwis_app/visit/cubit/visit_cubit.dart';
import 'package:serwis_app/visit/repository/visit_repository.dart';

class VisitFormState {
  final VisitFormStatus status;
  final List<FormFieldSchema> formFields;
  final dynamic error;

  const VisitFormState({
    required this.status,
    required this.formFields,
    this.error,
  });

  factory VisitFormState.initial() {
    return const VisitFormState(
      status: VisitFormStatus.loading,
      formFields: [],
    );
  }

  VisitFormState copyWith({
    VisitFormStatus? status,
    List<FormFieldSchema>? formFields,
    dynamic error,
  }) {
    return VisitFormState(
      status: status ?? this.status,
      formFields: formFields ?? this.formFields,
      error: error ?? this.error,
    );
  }
}

enum VisitFormStatus {
  loading,
  inProgress,
  submitting,
  done,
}

class VisitFormCubit extends Cubit<VisitFormState> {
  final Visit visit;
  final VisitRepository visitRepository;
  // final VisitCubit visitCubit;

  VisitFormCubit({
    required this.visit,
    required this.visitRepository,
    // required this.visitCubit,
  }) : super(VisitFormState.initial());

  void getVisitForm(VisitFormType formType) async {
    try {
      emit(state.copyWith(status: VisitFormStatus.loading));

      final formFields = await visitRepository.getVisitForms(visit.id, formType);

      emit(state.copyWith(
        status: VisitFormStatus.inProgress,
        formFields: formFields,
      ));
    } catch (e) {
      emit(state.copyWith(status: VisitFormStatus.inProgress, error: e));
    }
  }

  Future<void> submitForm(Map<String, dynamic> formData) async {
    try {
      emit(state.copyWith(status: VisitFormStatus.submitting));

      await visitRepository.submitVisitForm(visit.id, VisitFormType.visitStart, formData);

      // visitCubit.onStartFormSubmitted();

      emit(state.copyWith(status: VisitFormStatus.done));
    } catch (e) {
      emit(state.copyWith(status: VisitFormStatus.inProgress, error: e));
    }
  }
}
