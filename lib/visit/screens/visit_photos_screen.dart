import 'package:flutter/material.dart';

import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/visit/widgets/visit_photos_list.dart';

class VisitPhotosScreen extends StatelessWidget {
  const VisitPhotosScreen({
    super.key,
    required this.visit,
  });

  final Visit visit;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Zdjęcia z wizyty'),
      ),
      body: SingleChildScrollView(
        child: VisitPhotosList(
          photos: visit.visitPhotos ?? [],
          device: visit.device!,
          visit: visit,
        ),
      ),
    );
  }
}

// import 'dart:io';

// import 'package:flutter/material.dart';

// import 'package:flutter_bloc/flutter_bloc.dart';

// import 'package:serwis_app/core/utils/context_extensions.dart';
// import 'package:serwis_app/visit/cubit/visit_photos_cubit.dart';
// import 'package:serwis_app/visit/widgets/photo_capture_view.dart';
// import 'package:serwis_app/visit/widgets/photo_management_view.dart';

// class VisitPhotosScreen extends StatefulWidget {
//   final String description;
//   final bool canSkip;

//   const VisitPhotosScreen({
//     super.key,
//     required this.description,
//     this.canSkip = false,
//   });

//   @override
//   State<VisitPhotosScreen> createState() => _VisitPhotosScreenState();
// }

// class _VisitPhotosScreenState extends State<VisitPhotosScreen> {
//   final List<File> _capturedPhotos = [];

//   void _handlePhotoTaken(File photoFile) {
//     setState(() {
//       _capturedPhotos.add(photoFile);
//     });

//     // Po zrobieniu zdjęcia przełączamy widok na zarządzanie zdjęciami
//     context.read<VisitPhotosCubit>().switchMode(VisitPhotosMode.managingPhotos);

//     // Informujemy użytkownika o sukcesie
//     context.showSnackbar('Zdjęcie zostało dodane');
//   }

//   void _handleSkip() {
//     if (widget.canSkip) {
//       Navigator.of(context).pop();
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return BlocProvider(
//       create: (context) => VisitPhotosCubit(),
//       child: BlocBuilder<VisitPhotosCubit, VisitPhotosState>(
//         builder: (context, state) {
//           return Scaffold(
//             appBar: AppBar(
//               title: Text(state.mode == VisitPhotosMode.takingPhotos ? 'Zrób zdjęcie' : 'Zarządzaj zdjęciami'),
//               actions: [
//                 if (_capturedPhotos.isNotEmpty)
//                   IconButton(
//                     icon: Icon(
//                       state.mode == VisitPhotosMode.takingPhotos ? Icons.photo_library : Icons.camera_alt,
//                     ),
//                     onPressed: () {
//                       final newMode = state.mode == VisitPhotosMode.takingPhotos
//                           ? VisitPhotosMode.managingPhotos
//                           : VisitPhotosMode.takingPhotos;
//                       context.read<VisitPhotosCubit>().switchMode(newMode);
//                     },
//                   ),
//               ],
//             ),
//             body: SafeArea(
//               child: Padding(
//                 padding: const EdgeInsets.all(16.0),
//                 child: state.mode == VisitPhotosMode.takingPhotos
//                     ? PhotoCaptureView(
//                         description: widget.description,
//                         canSkip: widget.canSkip,
//                         onPhotoTaken: _handlePhotoTaken,
//                         onSkip: _handleSkip,
//                       )
//                     : const PhotoManagementView(),
//               ),
//             ),
//             bottomNavigationBar: _buildBottomBar(context, state),
//           );
//         },
//       ),
//     );
//   }

//   Widget? _buildBottomBar(BuildContext context, VisitPhotosState state) {
//     if (_capturedPhotos.isEmpty) {
//       return null;
//     }

//     return BottomAppBar(
//       child: Padding(
//         padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             Text(
//               'Zdjęcia: ${_capturedPhotos.length}',
//               style: Theme.of(context).textTheme.bodyLarge,
//             ),
//             ElevatedButton(
//               onPressed: () {
//                 Navigator.of(context).pop(_capturedPhotos);
//               },
//               child: const Text('Zakończ'),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
