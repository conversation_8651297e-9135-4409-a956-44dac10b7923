import 'dart:convert';

import 'package:serwis_app/core/models/device.dart';
import 'package:serwis_app/core/models/location.dart';
import 'package:serwis_app/core/models/serviceman.dart';
import 'package:serwis_app/core/models/task.dart';
import 'package:serwis_app/core/models/visit/visit_photo.dart';

enum VisitModelStatus {
  pending('pending'),
  planned('planned'),
  attended('attended'),
  completed('completed'),
  cancelled('cancelled'),
  notAvailable("not_available");

  final String value;

  const VisitModelStatus(this.value);

  @override
  String toString() {
    return value;
  }

  factory VisitModelStatus.fromString(String? value) {
    return VisitModelStatus.values.firstWhere(
      (element) => element.value == value,
      orElse: () => VisitModelStatus.notAvailable,
    );
  }
}

class Visit {
  final int id;
  final int incidentId;
  final int visitNumber;
  final VisitModelStatus status;
  final Serviceman? serviceman;
  final Location? location;
  final List<Task> tasks;
  final DateTime? plannedDate;
  final List<VisitPhoto>? visitPhotos;
  final Device? device;
  final bool isSurveyRelated;
  final bool isInstallationOnly;
  final DateTime? visitAttendedTime;

  const Visit({
    required this.id,
    required this.incidentId,
    required this.visitNumber,
    required this.status,
    required this.serviceman,
    required this.location,
    required this.tasks,
    required this.visitPhotos,
    required this.plannedDate,
    required this.device,
    required this.isSurveyRelated,
    required this.isInstallationOnly,
    required this.visitAttendedTime,
  });

  factory Visit.fromMap(Map<String, dynamic> map) {
    return Visit(
      id: map['id']?.toInt() ?? 0,
      incidentId: map['incidentId']?.toInt() ?? 0,
      visitNumber: map['visitNumber']?.toInt() ?? 0,
      status: VisitModelStatus.fromString(map['status']),
      serviceman: map['serviceman'] != null ? Serviceman.fromMap(map['serviceman']) : null,
      location: map['location'] != null ? Location.fromMap(map['location']) : null,
      tasks: List<Task>.from(map['tasks']?.map((x) => Task.fromMap(x))),
      visitPhotos: map['visitPhotos'] != null
          ? List<VisitPhoto>.from(map['visitPhotos']?.map((x) => VisitPhoto.fromMap(x)))
          : null,
      plannedDate: map['plannedDate'] != null ? DateTime.parse(map['plannedDate']).toLocal() : null,
      device: map['device'] != null ? Device.fromMap(map['device']) : null,
      isSurveyRelated: map['surveyRelated'] ?? false,
      isInstallationOnly: map['installationOnly'] ?? false,
      visitAttendedTime: map['visitAttendedTime'] != null ? DateTime.parse(map['visitAttendedTime']).toLocal() : null,
    );
  }

  factory Visit.fromJson(String source) => Visit.fromMap(json.decode(source));

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'incidentId': incidentId,
      'visitNumber': visitNumber,
      'status': status.value,
      'serviceman': serviceman?.toMap(),
      'location': location?.toMap(),
      'tasks': tasks.map((x) => x.toMap()).toList(),
      'visitPhotos': visitPhotos?.map((x) => x.toMap()).toList(),
      'plannedDate': plannedDate?.toIso8601String(),
      'device': device?.toMap(),
      'surveyRelated': isSurveyRelated,
      'installationOnly': isInstallationOnly,
      'visitAttendedTime': visitAttendedTime?.toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());

  Visit copyWith({
    VisitModelStatus? status,
    DateTime? visitAttendedTime,
  }) {
    return Visit(
      id: id,
      incidentId: incidentId,
      visitNumber: visitNumber,
      status: status ?? this.status,
      serviceman: serviceman,
      location: location,
      tasks: tasks,
      visitPhotos: visitPhotos,
      plannedDate: plannedDate,
      device: device,
      isSurveyRelated: isSurveyRelated,
      isInstallationOnly: isInstallationOnly,
      visitAttendedTime: visitAttendedTime ?? this.visitAttendedTime,
    );
  }
}
