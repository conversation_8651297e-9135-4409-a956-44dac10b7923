import 'dart:convert';

class VisitPhoto {
  final int id;
  final String photoFileName;
  final String category;
  final String? subCategory;
  final String fileContent; // New field for base64 encoded data

  VisitPhoto({
    required this.id,
    required this.photoFileName,
    required this.category,
    this.subCategory,
    required this.fileContent,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'photoFileName': photoFileName,
      'category': category,
      'subCategory': subCategory,
      'fileContent': fileContent,
    };
  }

  factory VisitPhoto.fromMap(Map<String, dynamic> map) {
    return VisitPhoto(
      id: map['id']?.toInt() ?? 0,
      photoFileName: map['photoFileName'] ?? '',
      category: map['category'] ?? '',
      subCategory: map['subCategory'],
      fileContent: map['fileContent'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory VisitPhoto.fromJson(String source) => VisitPhoto.fromMap(json.decode(source));
}
