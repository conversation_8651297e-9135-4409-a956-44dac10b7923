import 'dart:convert';

enum TaskStatus {
  pending("pending"),
  planned("planned"),
  notApplicable("not_applicable"),
  completed("completed"),
  notAvailable("not_available");

  final String? value;

  const TaskStatus(this.value);

  factory TaskStatus.fromString(String? value) {
    return TaskStatus.values.firstWhere(
      (element) => element.value == value,
      orElse: () => TaskStatus.notAvailable,
    );
  }
}

class Task {
  final int id;
  final DateTime? ctime;
  final DateTime? etime;
  final TaskStatus status;
  final String? symptom;
  final String? description;
  final DateTime? targetTime;
  final String? servicemanSolution;

  const Task({
    required this.id,
    required this.ctime,
    required this.etime,
    required this.status,
    required this.symptom,
    required this.description,
    required this.targetTime,
    required this.servicemanSolution,
  });

  factory Task.fromMap(Map<String, dynamic> map) {
    return Task(
      id: map['id']?.toInt() ?? 0,
      ctime: map['ctime'] != null ? DateTime.tryParse(map['ctime'])!.toLocal() : null,
      etime: map['etime'] != null ? DateTime.tryParse(map['etime'])!.toLocal() : null,
      status: TaskStatus.fromString(map['status']),
      symptom: map['symptom'],
      description: map['description'],
      targetTime: map['targetTime'] != null ? DateTime.tryParse(map['targetTime']) : null,
      servicemanSolution: map['servicemanSolution'],
    );
  }

  factory Task.fromJson(String source) => Task.fromMap(json.decode(source));

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'ctime': ctime?.toIso8601String(),
      'etime': etime?.toIso8601String(),
      'status': status.value,
      'symptom': symptom,
      'description': description,
      'targetTime': targetTime?.toIso8601String(),
      'servicemanSolution': servicemanSolution,
    };
  }

  String toJson() => json.encode(toMap());
}
